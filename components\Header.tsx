
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { SearchIcon, UserIcon, ShoppingCartIcon, LogoutIcon } from './common/Icon';

const Header: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, profile, signOut } = useAuth();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isContactUsInView, setIsContactUsInView] = useState(false);
  const [showUserDropdown, setShowUserDropdown] = useState(false);

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Store', path: '/store' },
    { name: 'About Us', path: '/about' },
    { name: 'Contact Us', path: 'contact' }, // Special handling for contact section
  ];
  const logoUrl = "/assets/logo.png";

  // Function to handle Contact Us navigation with smooth scrolling
  const handleContactUsNavigation = () => {
    // First navigate to Home page if not already there
    if (location.pathname !== '/') {
      navigate('/');
      // Wait for navigation to complete, then scroll
      setTimeout(() => {
        const contactSection = document.getElementById('contact-us');
        if (contactSection) {
          contactSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      }, 100);
    } else {
      // Already on Home page, just scroll to ContactUs section
      const contactSection = document.getElementById('contact-us');
      if (contactSection) {
        contactSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
    }
  };

  // Handle user sign out
  const handleSignOut = async () => {
    const { error } = await signOut();
    if (!error) {
      setShowUserDropdown(false);
      navigate('/');
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.user-dropdown')) {
        setShowUserDropdown(false);
      }
    };

    if (showUserDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showUserDropdown]);

  // Intersection Observer for ContactUs section visibility
  useEffect(() => {
    // Only run the observer when we're on the Home page
    if (location.pathname !== '/') {
      setIsContactUsInView(false);
      return;
    }

    const contactSection = document.getElementById('contact-us');

    if (!contactSection) {
      setIsContactUsInView(false);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        // Set ContactUs as in view when 50% of the section is visible
        setIsContactUsInView(entry.isIntersecting);
      },
      {
        threshold: 0.5, // 50% of the section must be visible
        rootMargin: '-80px 0px -80px 0px', // Account for header height and some padding
      }
    );

    observer.observe(contactSection);

    return () => {
      observer.disconnect();
    };
  }, [location.pathname]); // Re-run when page changes to ensure observer is set up correctly

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header
      className={`text-white sticky top-0 z-50 transition-all duration-500 ease-in-out ${
        isScrolled
          ? 'bg-brand-accent-teal/20 backdrop-blur-xl shadow-2xl'
          : 'bg-brand-accent-teal shadow-lg'
      }`}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <div className="flex items-center">
            <img src={logoUrl} alt="Le Pristine Logo" className="h-12 w-auto" />
          </div>

          {/* Navigation Links */}
          <nav className="hidden md:flex space-x-8">
            {navItems.map((item) => (
              <a
                key={item.name}
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  if (item.path === 'contact') {
                    handleContactUsNavigation();
                  } else {
                    navigate(item.path);
                  }
                }}
                className={`font-medium transition-colors duration-200 ease-in-out py-2
                  ${(() => {
                    // Contact Us link: highlight only when ContactUs section is in viewport
                    if (item.path === 'contact') {
                      return isContactUsInView
                        ? 'text-brand-main-red font-semibold border-b-2 border-brand-main-red'
                        : 'text-white hover:text-brand-main-red-darker';
                    }
                    // Home link: highlight only when on Home page AND ContactUs section is NOT in viewport
                    if (item.path === '/') {
                      return (location.pathname === '/' && !isContactUsInView)
                        ? 'text-brand-main-red font-semibold border-b-2 border-brand-main-red'
                        : 'text-white hover:text-brand-main-red-darker';
                    }
                    // Other links: normal page-based highlighting
                    return location.pathname === item.path
                      ? 'text-brand-main-red font-semibold border-b-2 border-brand-main-red'
                      : 'text-white hover:text-brand-main-red-darker';
                  })()}`}
                aria-current={(() => {
                  if (item.path === 'contact') {
                    return isContactUsInView ? 'page' : undefined;
                  }
                  if (item.path === '/') {
                    return (location.pathname === '/' && !isContactUsInView) ? 'page' : undefined;
                  }
                  return location.pathname === item.path ? 'page' : undefined;
                })()}
                aria-label={`Navigate to ${item.name}`}
              >
                {item.name}
              </a>
            ))}
          </nav>

          {/* Search and Icons */}
          <div className="flex items-center space-x-3 sm:space-x-4">
            <div className="relative hidden sm:block">
              <input
                type="search"
                placeholder="Search"
                aria-label="Search products"
                className="pl-10 pr-4 py-2.5 w-40 md:w-48 rounded-full border bg-transparent border-white/40 placeholder-white/70 text-white focus:ring-white focus:border-white transition-all text-sm"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <SearchIcon className="text-white/70" size={18} />
              </div>
            </div>
            {/* User Account Button with Dropdown */}
            <div className="relative user-dropdown">
              <button
                onClick={() => {
                  if (user) {
                    setShowUserDropdown(!showUserDropdown);
                  } else {
                    navigate('/login');
                  }
                }}
                className="p-2.5 rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                aria-label={user ? "User account menu" : "Sign in"}
              >
                <UserIcon size={18} />
              </button>

              {/* User Dropdown Menu */}
              {user && showUserDropdown && (
                <div className="absolute right-0 mt-2 w-64 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 py-2 z-50">
                  {/* User Info */}
                  <div className="px-4 py-3 border-b border-gray-200/50">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-brand-accent-teal to-brand-accent-teal-darker rounded-full flex items-center justify-center">
                        <UserIcon className="text-white" size={16} />
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900">
                          {profile?.first_name} {profile?.last_name}
                        </p>
                        <p className="text-sm text-gray-600">{user.email}</p>
                      </div>
                    </div>
                  </div>

                  {/* Menu Items */}
                  <div className="py-1">
                    <button
                      onClick={() => {
                        navigate('/profile');
                        setShowUserDropdown(false);
                      }}
                      className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100/50 transition-colors flex items-center space-x-2"
                    >
                      <UserIcon size={16} />
                      <span>My Profile</span>
                    </button>

                    <button
                      onClick={handleSignOut}
                      className="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50/50 transition-colors flex items-center space-x-2"
                    >
                      <LogoutIcon size={16} />
                      <span>Sign Out</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
            <button
              className="relative p-2.5 rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
              aria-label="View shopping cart with 8 items"
            >
              <ShoppingCartIcon size={18} />
              <span className="absolute -top-1.5 -right-1.5 bg-brand-main-red text-white text-xs w-5 h-5 rounded-full flex items-center justify-center font-semibold">8</span>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
